import dayjs from 'dayjs';
import { FEATURE_FLAGS } from '@/constants';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { Button } from '@/libs/ui/Button/Button';
import PlusIcon from '@/assets/images/plus.svg?react';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { OrderHistoryItemContent } from '../OrderHistoryItemContent/OrderHistoryItemContent';
import { Icon } from '@/libs/icons/Icon';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';

export const OrderHistoryRegularItem = ({
  item,
}: {
  item: OrderHistoryDetailItemType;
}) => {
  const [shipment] = item.shipments ?? [];
  const dateDelivered =
    item.status === 'DELIVERED' ? shipment?.dateDelivered || null : null;

  const etaDate = [
    'PENDING',
    'PROCESSING',
    'PARTIALLY_SHIPPED',
    'ACCEPTED',
    'SHIPPED',
  ].includes(item.status)
    ? shipment?.etaDateTo || shipment?.etaDateFrom || null
    : null;

  return (
    <div className="grid w-full grid-cols-[110px_1fr] gap-1 pb-4">
      <ProductImage
        product={{ ...item.product, productOfferId: item.productOfferId }}
        className="h-24 w-full"
      />
      <div className="grid w-full grid-cols-[1fr_150px] gap-5">
        <OrderHistoryItemContent item={item} />
        <div className="flex flex-col gap-1 text-right">
          <p className="mb-1 text-xs font-medium text-gray-500/70">Status</p>
          {item.status && (
            <>
              <OrderStatus
                status={item.status}
                align="end"
                trackLink={shipment?.trackingLink ?? ''}
              />

              {dateDelivered ? (
                <p className="text-[10px] text-[#666]">
                  {dayjs(dateDelivered).format('MMMM D, YYYY')}
                </p>
              ) : (
                etaDate && (
                  <p className="flex justify-end gap-1 text-[10px] whitespace-nowrap text-[#666]">
                    Expected Delivery:
                    <span className="font-bold text-[#333]">
                      {dayjs(etaDate).format('MMMM D, YYYY')}
                    </span>
                  </p>
                )
              )}
            </>
          )}
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <Button className="mt-2">
              <Icon name="cartSummary" size={'1.3rem'} />
              <PlusIcon />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
